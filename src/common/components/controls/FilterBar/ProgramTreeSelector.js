import PropTypes from 'prop-types';
import React, { useState, useEffect, useCallback, useMemo } from 'react';

import CommonTreeSelectorBase from '../../../../common/components/controls/CommonTreeSelector';
import {
  resolveNodeParentId,
  isProgram,
  resolveNodeId,
  canRender,
} from '../../../../model/ProgramGroupProgramTypeNames';
import nodeSortingOptionsSequence from '../../../utils/nodeSortingOptionsSequence';
import StaticTreeDefaultFilter from '../../dataViews/StaticTree/StaticTreeDefaultFilter';

const ProgramTreeSelector = ({ programs, value, onChange, ...props }) => {
  const [selectedNodes, setSelectedNodes] = useState([]);

  const findLeafNodes = data => data.filter(item => isProgram(item));

  useEffect(() => {
    if (!Array.isArray(value)) return;
    const selected = findLeafNodes(value);
    setSelectedNodes(selected);
  }, [value]);

  const _onChange = useCallback(
    value => {
      const selected = findLeafNodes(value);
      onChange(selected);
    },
    [onChange],
  );

  const makeStatusMatcher = useCallback(
    statusValue =>
      statusValue ? ({ status }) => status === statusValue : null,
    [],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'status',
        predicate: makeStatusMatcher,
        includeChildren: false,
        collectVisibleIds: true,
      },
    ],
    [makeStatusMatcher],
  );

  return (
    <CommonTreeSelectorBase
      {...props}
      hasSyntheticRootNode
      adapter={{
        resolveNodeId,
        resolveNodeParentId,
        nodeIsLeaf: isProgram,
        canRender,
      }}
      filter={{
        component: StaticTreeDefaultFilter,
        predicatesMeta,
      }}
      models={programs}
      plugins={{
        nodeSortingOptions: nodeSortingOptionsSequence,
      }}
      treeName="ProgramTreeSelector"
      value={selectedNodes}
      onChange={_onChange}
    />
  );
};
ProgramTreeSelector.propTypes = {
  programs: PropTypes.array,
  ...CommonTreeSelectorBase.propTypes,
};

ProgramTreeSelector.defaultProps = {
  programs: [],
  ...CommonTreeSelectorBase.defaultProps,
};

export default ProgramTreeSelector;
